"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.normDependencies = void 0;

var _dependenciesAbsGenerated = require("./dependenciesAbs.generated.js");

var _dependenciesAddGenerated = require("./dependenciesAdd.generated.js");

var _dependenciesConjGenerated = require("./dependenciesConj.generated.js");

var _dependenciesCtransposeGenerated = require("./dependenciesCtranspose.generated.js");

var _dependenciesEigsGenerated = require("./dependenciesEigs.generated.js");

var _dependenciesEqualScalarGenerated = require("./dependenciesEqualScalar.generated.js");

var _dependenciesLargerGenerated = require("./dependenciesLarger.generated.js");

var _dependenciesMatrixGenerated = require("./dependenciesMatrix.generated.js");

var _dependenciesMultiplyGenerated = require("./dependenciesMultiply.generated.js");

var _dependenciesPowGenerated = require("./dependenciesPow.generated.js");

var _dependenciesSmallerGenerated = require("./dependenciesSmaller.generated.js");

var _dependenciesSqrtGenerated = require("./dependenciesSqrt.generated.js");

var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");

var _factoriesAny = require("../../factoriesAny.js");

/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
var normDependencies = {
  absDependencies: _dependenciesAbsGenerated.absDependencies,
  addDependencies: _dependenciesAddGenerated.addDependencies,
  conjDependencies: _dependenciesConjGenerated.conjDependencies,
  ctransposeDependencies: _dependenciesCtransposeGenerated.ctransposeDependencies,
  eigsDependencies: _dependenciesEigsGenerated.eigsDependencies,
  equalScalarDependencies: _dependenciesEqualScalarGenerated.equalScalarDependencies,
  largerDependencies: _dependenciesLargerGenerated.largerDependencies,
  matrixDependencies: _dependenciesMatrixGenerated.matrixDependencies,
  multiplyDependencies: _dependenciesMultiplyGenerated.multiplyDependencies,
  powDependencies: _dependenciesPowGenerated.powDependencies,
  smallerDependencies: _dependenciesSmallerGenerated.smallerDependencies,
  sqrtDependencies: _dependenciesSqrtGenerated.sqrtDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  createNorm: _factoriesAny.createNorm
};
exports.normDependencies = normDependencies;