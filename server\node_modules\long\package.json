{"name": "long", "version": "2.4.0", "author": "<PERSON> <<EMAIL>>", "description": "A Long class for representing a 64-bit two's-complement integer value.", "main": "dist/Long.js", "repository": {"type": "git", "url": "https://github.com/dcodeIO/Long.js.git"}, "keywords": ["math"], "dependencies": {}, "devDependencies": {"testjs": "latest", "closurecompiler": "latest", "metascript": "~0"}, "license": "Apache-2.0", "engines": {"node": ">=0.6"}, "scripts": {"build": "node scripts/build.js", "make": "npm run-script build && npm run-script compile && npm run-script compress && npm test", "compile": "ccjs dist/Long.js --compilation_level=ADVANCED_OPTIMIZATIONS --create_source_map=dist/Long.min.map > dist/Long.min.js", "compress": "gzip -c -9 dist/Long.min.js > dist/Long.min.js.gz", "test": "node node_modules/testjs/bin/testjs tests/suite.js"}}