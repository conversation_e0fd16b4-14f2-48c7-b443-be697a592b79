# lodash.assignin v4.2.0

The [lodash](https://lodash.com/) method `_.assignIn` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.assignin
```

In Node.js:
```js
var assignIn = require('lodash.assignin');
```

See the [documentation](https://lodash.com/docs#assignIn) or [package source](https://github.com/lodash/lodash/blob/4.2.0-npm-packages/lodash.assignin) for more details.
