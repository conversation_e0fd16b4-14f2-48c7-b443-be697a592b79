<?php return array(
    'root' => array(
        'pretty_version' => 'v2.11.0',
        'version' => '2.11.0.0',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => '7db9eb40c8ba887e81c0fe84f2888a967396cdfb',
        'name' => 'google/apiclient',
        'dev' => false,
    ),
    'versions' => array(
        'firebase/php-jwt' => array(
            'pretty_version' => 'v5.4.0',
            'version' => '5.4.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'reference' => 'd2113d9b2e0e349796e72d2a63cf9319100382d2',
            'dev_requirement' => false,
        ),
        'google/apiclient' => array(
            'pretty_version' => 'v2.11.0',
            'version' => '2.11.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => '7db9eb40c8ba887e81c0fe84f2888a967396cdfb',
            'dev_requirement' => false,
        ),
        'google/apiclient-services' => array(
            'pretty_version' => 'v0.213.0',
            'version' => '0.213.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/apiclient-services',
            'aliases' => array(),
            'reference' => '260311821505438eb9208b068da0d849b8ea9baa',
            'dev_requirement' => false,
        ),
        'google/auth' => array(
            'pretty_version' => 'v1.18.0',
            'version' => '1.18.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../google/auth',
            'aliases' => array(),
            'reference' => '21dd478e77b0634ed9e3a68613f74ed250ca9347',
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '6.5.5',
            'version' => '6.5.5.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'reference' => '9d4290de1cfd701f38099ef7e183b64b4b7b0c5e',
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '1.4.1',
            'version' => '1.4.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'reference' => '8e7d04f1f6450fef59366c399cfad4b9383aa30d',
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '1.8.2',
            'version' => '1.8.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'reference' => 'dc960a912984efb74d0a90222870c72c87f10c91',
            'dev_requirement' => false,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '1.26.1',
            'version' => '1.26.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'reference' => 'c6b00f05152ae2c9b04a448f99c7590beb6042f5',
            'dev_requirement' => false,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v1.0.4',
            'version' => '1.0.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'reference' => '2132f0f293d856026d7d11bd81b9f4a23a1dc1f6',
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v2.0.20',
            'version' => '2.0.20.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'reference' => '0f1f60250fccffeaf5dda91eea1c018aed1adc2a',
            'dev_requirement' => false,
        ),
        'phpseclib/phpseclib' => array(
            'pretty_version' => '3.0.10',
            'version' => '3.0.10.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpseclib/phpseclib',
            'aliases' => array(),
            'reference' => '62fcc5a94ac83b1506f52d7558d828617fac9187',
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'reference' => 'd11b50ad223250cf17b86e38383413f5a6764bf8',
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'reference' => 'f6561bf28d520154e4b0ec72be95418abe6d9363',
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.19.0',
            'version' => '1.19.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'reference' => '4ad5115c0f5d5172a9fe8147675ec6de266d8826',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.19.0',
            'version' => '1.19.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'reference' => '8db0ae7936b42feb370840cf24de1a144fb0ef27',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php70' => array(
            'pretty_version' => 'v1.19.0',
            'version' => '1.19.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php70',
            'aliases' => array(),
            'reference' => '3fe414077251a81a1b15b1c709faf5c2fbae3d4e',
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php72' => array(
            'pretty_version' => 'v1.19.0',
            'version' => '1.19.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php72',
            'aliases' => array(),
            'reference' => 'beecef6b463b06954638f02378f52496cb84bacc',
            'dev_requirement' => false,
        ),
    ),
);
