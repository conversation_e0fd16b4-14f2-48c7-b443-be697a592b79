<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname(dirname($vendorDir));

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Mobile_Detect' => $vendorDir . '/mobiledetect/mobiledetectlib/Mobile_Detect.php',
    'Paymentwall_ApiObject' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/ApiObject.php',
    'Paymentwall_ApiObjectInterface' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/ApiObjectInterface.php',
    'Paymentwall_Base' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Base.php',
    'Paymentwall_Card' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Card.php',
    'Paymentwall_Charge' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Charge.php',
    'Paymentwall_Config' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Config.php',
    'Paymentwall_GenerericApiObject' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/GenerericApiObject.php',
    'Paymentwall_HttpAction' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/HttpAction.php',
    'Paymentwall_Instance' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Instance.php',
    'Paymentwall_Mobiamo' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Mobiamo.php',
    'Paymentwall_OneTimeToken' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/OneTimeToken.php',
    'Paymentwall_Pingback' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Pingback.php',
    'Paymentwall_Product' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Product.php',
    'Paymentwall_Response_Abstract' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Response/Abstract.php',
    'Paymentwall_Response_Error' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Response/Error.php',
    'Paymentwall_Response_Factory' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Response/Factory.php',
    'Paymentwall_Response_Interface' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Response/Interface.php',
    'Paymentwall_Response_Success' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Response/Success.php',
    'Paymentwall_Signature_Abstract' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Signature/Abstract.php',
    'Paymentwall_Signature_Pingback' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Signature/Pingback.php',
    'Paymentwall_Signature_Widget' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Signature/Widget.php',
    'Paymentwall_Subscription' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Subscription.php',
    'Paymentwall_Widget' => $vendorDir . '/paymentwall/paymentwall-php/lib/Paymentwall/Widget.php',
);
