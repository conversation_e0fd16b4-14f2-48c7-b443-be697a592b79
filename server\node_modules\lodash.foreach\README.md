# lodash.foreach v4.5.0

The [lodash](https://lodash.com/) method `_.forEach` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.foreach
```

In Node.js:
```js
var forEach = require('lodash.foreach');
```

See the [documentation](https://lodash.com/docs#forEach) or [package source](https://github.com/lodash/lodash/blob/4.5.0-npm-packages/lodash.foreach) for more details.
