# lodash.bind v4.2.1

The [lodash](https://lodash.com/) method `_.bind` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.bind
```

In Node.js:
```js
var bind = require('lodash.bind');
```

See the [documentation](https://lodash.com/docs#bind) or [package source](https://github.com/lodash/lodash/blob/4.2.1-npm-packages/lodash.bind) for more details.
