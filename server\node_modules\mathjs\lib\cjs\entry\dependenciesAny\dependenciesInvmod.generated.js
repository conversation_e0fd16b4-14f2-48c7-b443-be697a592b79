"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.invmodDependencies = void 0;

var _dependenciesBigNumberClassGenerated = require("./dependenciesBigNumberClass.generated.js");

var _dependenciesAddGenerated = require("./dependenciesAdd.generated.js");

var _dependenciesEqualGenerated = require("./dependenciesEqual.generated.js");

var _dependenciesIsIntegerGenerated = require("./dependenciesIsInteger.generated.js");

var _dependenciesModGenerated = require("./dependenciesMod.generated.js");

var _dependenciesSmallerGenerated = require("./dependenciesSmaller.generated.js");

var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");

var _dependenciesXgcdGenerated = require("./dependenciesXgcd.generated.js");

var _factoriesAny = require("../../factoriesAny.js");

/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
var invmodDependencies = {
  BigNumberDependencies: _dependenciesBigNumberClassGenerated.BigNumberDependencies,
  addDependencies: _dependenciesAddGenerated.addDependencies,
  equalDependencies: _dependenciesEqualGenerated.equalDependencies,
  isIntegerDependencies: _dependenciesIsIntegerGenerated.isIntegerDependencies,
  modDependencies: _dependenciesModGenerated.modDependencies,
  smallerDependencies: _dependenciesSmallerGenerated.smallerDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  xgcdDependencies: _dependenciesXgcdGenerated.xgcdDependencies,
  createInvmod: _factoriesAny.createInvmod
};
exports.invmodDependencies = invmodDependencies;