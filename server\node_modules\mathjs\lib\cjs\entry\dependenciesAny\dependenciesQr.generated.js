"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.qrDependencies = void 0;

var _dependenciesAddScalarGenerated = require("./dependenciesAddScalar.generated.js");

var _dependenciesComplexGenerated = require("./dependenciesComplex.generated.js");

var _dependenciesConjGenerated = require("./dependenciesConj.generated.js");

var _dependenciesDivideScalarGenerated = require("./dependenciesDivideScalar.generated.js");

var _dependenciesEqualGenerated = require("./dependenciesEqual.generated.js");

var _dependenciesIdentityGenerated = require("./dependenciesIdentity.generated.js");

var _dependenciesIsZeroGenerated = require("./dependenciesIsZero.generated.js");

var _dependenciesMatrixGenerated = require("./dependenciesMatrix.generated.js");

var _dependenciesMultiplyScalarGenerated = require("./dependenciesMultiplyScalar.generated.js");

var _dependenciesSignGenerated = require("./dependenciesSign.generated.js");

var _dependenciesSqrtGenerated = require("./dependenciesSqrt.generated.js");

var _dependenciesSubtractGenerated = require("./dependenciesSubtract.generated.js");

var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");

var _dependenciesUnaryMinusGenerated = require("./dependenciesUnaryMinus.generated.js");

var _dependenciesZerosGenerated = require("./dependenciesZeros.generated.js");

var _factoriesAny = require("../../factoriesAny.js");

/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
var qrDependencies = {
  addScalarDependencies: _dependenciesAddScalarGenerated.addScalarDependencies,
  complexDependencies: _dependenciesComplexGenerated.complexDependencies,
  conjDependencies: _dependenciesConjGenerated.conjDependencies,
  divideScalarDependencies: _dependenciesDivideScalarGenerated.divideScalarDependencies,
  equalDependencies: _dependenciesEqualGenerated.equalDependencies,
  identityDependencies: _dependenciesIdentityGenerated.identityDependencies,
  isZeroDependencies: _dependenciesIsZeroGenerated.isZeroDependencies,
  matrixDependencies: _dependenciesMatrixGenerated.matrixDependencies,
  multiplyScalarDependencies: _dependenciesMultiplyScalarGenerated.multiplyScalarDependencies,
  signDependencies: _dependenciesSignGenerated.signDependencies,
  sqrtDependencies: _dependenciesSqrtGenerated.sqrtDependencies,
  subtractDependencies: _dependenciesSubtractGenerated.subtractDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  unaryMinusDependencies: _dependenciesUnaryMinusGenerated.unaryMinusDependencies,
  zerosDependencies: _dependenciesZerosGenerated.zerosDependencies,
  createQr: _factoriesAny.createQr
};
exports.qrDependencies = qrDependencies;