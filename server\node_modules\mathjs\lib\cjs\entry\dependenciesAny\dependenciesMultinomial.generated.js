"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.multinomialDependencies = void 0;

var _dependenciesAddGenerated = require("./dependenciesAdd.generated.js");

var _dependenciesDivideGenerated = require("./dependenciesDivide.generated.js");

var _dependenciesFactorialGenerated = require("./dependenciesFactorial.generated.js");

var _dependenciesIsIntegerGenerated = require("./dependenciesIsInteger.generated.js");

var _dependenciesIsPositiveGenerated = require("./dependenciesIsPositive.generated.js");

var _dependenciesMultiplyGenerated = require("./dependenciesMultiply.generated.js");

var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");

var _factoriesAny = require("../../factoriesAny.js");

/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
var multinomialDependencies = {
  addDependencies: _dependenciesAddGenerated.addDependencies,
  divideDependencies: _dependenciesDivideGenerated.divideDependencies,
  factorialDependencies: _dependenciesFactorialGenerated.factorialDependencies,
  isIntegerDependencies: _dependenciesIsIntegerGenerated.isIntegerDependencies,
  isPositiveDependencies: _dependenciesIsPositiveGenerated.isPositiveDependencies,
  multiplyDependencies: _dependenciesMultiplyGenerated.multiplyDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  createMultinomial: _factoriesAny.createMultinomial
};
exports.multinomialDependencies = multinomialDependencies;