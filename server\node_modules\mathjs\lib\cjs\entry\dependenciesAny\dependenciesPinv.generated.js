"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.pinvDependencies = void 0;

var _dependenciesComplexClassGenerated = require("./dependenciesComplexClass.generated.js");

var _dependenciesAddGenerated = require("./dependenciesAdd.generated.js");

var _dependenciesCtransposeGenerated = require("./dependenciesCtranspose.generated.js");

var _dependenciesDeepEqualGenerated = require("./dependenciesDeepEqual.generated.js");

var _dependenciesDivideScalarGenerated = require("./dependenciesDivideScalar.generated.js");

var _dependenciesDotGenerated = require("./dependenciesDot.generated.js");

var _dependenciesDotDivideGenerated = require("./dependenciesDotDivide.generated.js");

var _dependenciesEqualGenerated = require("./dependenciesEqual.generated.js");

var _dependenciesInvGenerated = require("./dependenciesInv.generated.js");

var _dependenciesMatrixGenerated = require("./dependenciesMatrix.generated.js");

var _dependenciesMultiplyGenerated = require("./dependenciesMultiply.generated.js");

var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");

var _factoriesAny = require("../../factoriesAny.js");

/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
var pinvDependencies = {
  ComplexDependencies: _dependenciesComplexClassGenerated.ComplexDependencies,
  addDependencies: _dependenciesAddGenerated.addDependencies,
  ctransposeDependencies: _dependenciesCtransposeGenerated.ctransposeDependencies,
  deepEqualDependencies: _dependenciesDeepEqualGenerated.deepEqualDependencies,
  divideScalarDependencies: _dependenciesDivideScalarGenerated.divideScalarDependencies,
  dotDependencies: _dependenciesDotGenerated.dotDependencies,
  dotDivideDependencies: _dependenciesDotDivideGenerated.dotDivideDependencies,
  equalDependencies: _dependenciesEqualGenerated.equalDependencies,
  invDependencies: _dependenciesInvGenerated.invDependencies,
  matrixDependencies: _dependenciesMatrixGenerated.matrixDependencies,
  multiplyDependencies: _dependenciesMultiplyGenerated.multiplyDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  createPinv: _factoriesAny.createPinv
};
exports.pinvDependencies = pinvDependencies;