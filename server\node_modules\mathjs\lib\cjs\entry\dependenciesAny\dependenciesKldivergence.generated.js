"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.kldivergenceDependencies = void 0;

var _dependenciesDivideGenerated = require("./dependenciesDivide.generated.js");

var _dependenciesDotDivideGenerated = require("./dependenciesDotDivide.generated.js");

var _dependenciesIsNumericGenerated = require("./dependenciesIsNumeric.generated.js");

var _dependenciesLogGenerated = require("./dependenciesLog.generated.js");

var _dependenciesMatrixGenerated = require("./dependenciesMatrix.generated.js");

var _dependenciesMultiplyGenerated = require("./dependenciesMultiply.generated.js");

var _dependenciesSumGenerated = require("./dependenciesSum.generated.js");

var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");

var _factoriesAny = require("../../factoriesAny.js");

/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
var kldivergenceDependencies = {
  divideDependencies: _dependenciesDivideGenerated.divideDependencies,
  dotDivideDependencies: _dependenciesDotDivideGenerated.dotDivideDependencies,
  isNumericDependencies: _dependenciesIsNumericGenerated.isNumericDependencies,
  logDependencies: _dependenciesLogGenerated.logDependencies,
  matrixDependencies: _dependenciesMatrixGenerated.matrixDependencies,
  multiplyDependencies: _dependenciesMultiplyGenerated.multiplyDependencies,
  sumDependencies: _dependenciesSumGenerated.sumDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  createKldivergence: _factoriesAny.createKldivergence
};
exports.kldivergenceDependencies = kldivergenceDependencies;