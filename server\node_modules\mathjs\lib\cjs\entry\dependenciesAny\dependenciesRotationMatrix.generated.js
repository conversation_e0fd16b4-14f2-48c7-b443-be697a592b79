"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.rotationMatrixDependencies = void 0;

var _dependenciesBigNumberClassGenerated = require("./dependenciesBigNumberClass.generated.js");

var _dependenciesDenseMatrixClassGenerated = require("./dependenciesDenseMatrixClass.generated.js");

var _dependenciesSparseMatrixClassGenerated = require("./dependenciesSparseMatrixClass.generated.js");

var _dependenciesAddScalarGenerated = require("./dependenciesAddScalar.generated.js");

var _dependenciesCosGenerated = require("./dependenciesCos.generated.js");

var _dependenciesMatrixGenerated = require("./dependenciesMatrix.generated.js");

var _dependenciesMultiplyScalarGenerated = require("./dependenciesMultiplyScalar.generated.js");

var _dependenciesNormGenerated = require("./dependenciesNorm.generated.js");

var _dependenciesSinGenerated = require("./dependenciesSin.generated.js");

var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");

var _dependenciesUnaryMinusGenerated = require("./dependenciesUnaryMinus.generated.js");

var _factoriesAny = require("../../factoriesAny.js");

/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
var rotationMatrixDependencies = {
  BigNumberDependencies: _dependenciesBigNumberClassGenerated.BigNumberDependencies,
  DenseMatrixDependencies: _dependenciesDenseMatrixClassGenerated.DenseMatrixDependencies,
  SparseMatrixDependencies: _dependenciesSparseMatrixClassGenerated.SparseMatrixDependencies,
  addScalarDependencies: _dependenciesAddScalarGenerated.addScalarDependencies,
  cosDependencies: _dependenciesCosGenerated.cosDependencies,
  matrixDependencies: _dependenciesMatrixGenerated.matrixDependencies,
  multiplyScalarDependencies: _dependenciesMultiplyScalarGenerated.multiplyScalarDependencies,
  normDependencies: _dependenciesNormGenerated.normDependencies,
  sinDependencies: _dependenciesSinGenerated.sinDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  unaryMinusDependencies: _dependenciesUnaryMinusGenerated.unaryMinusDependencies,
  createRotationMatrix: _factoriesAny.createRotationMatrix
};
exports.rotationMatrixDependencies = rotationMatrixDependencies;