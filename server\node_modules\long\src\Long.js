/**
 * Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers.
 *  See the from* functions below for more convenient ways of constructing Longs.
 * @exports Long
 * @class A Long class for representing a 64 bit two's-complement integer value.
 * @param {number} low The low (signed) 32 bits of the long
 * @param {number} high The high (signed) 32 bits of the long
 * @param {boolean=} unsigned Whether unsigned or not, defaults to `false` for signed
 * @constructor
 */
function Long(low, high, unsigned) {

    /**
     * The low 32 bits as a signed value.
     * @type {number}
     * @expose
     */
    this.low = low|0;

    /**
     * The high 32 bits as a signed value.
     * @type {number}
     * @expose
     */
    this.high = high|0;

    /**
     * Whether unsigned or not.
     * @type {boolean}
     * @expose
     */
    this.unsigned = !!unsigned;
}

// The internal representation of a long is the two given signed, 32-bit values.
// We use 32-bit pieces because these are the size of integers on which
// Javascript performs bit-operations.  For operations like addition and
// multiplication, we split each number into 16 bit pieces, which can easily be
// multiplied within Javascript's floating-point representation without overflow
// or change in sign.
//
// In the algorithms below, we frequently reduce the negative case to the
// positive case by negating the input(s) and then post-processing the result.
// Note that we must ALWAYS check specially whether those values are MIN_VALUE
// (-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as
// a positive number, it overflows back into a negative).  Not handling this
// case would often result in infinite recursion.
//
// Common constant values ZERO, ONE, NEG_ONE, etc. are defined below the from*
// methods on which they depend.

/**
 * An indicator used to reliably determine if an object is a Long or not.
 * @type {boolean}
 * @const
 * @expose
 * @private
 */
Long.__isLong__;

Object.defineProperty(Long.prototype, "__isLong__", {
    value: true,
    enumerable: false,
    configurable: false
});

/**
 * Tests if the specified object is a Long.
 * @param {*} obj Object
 * @returns {boolean}
 * @expose
 */
Long.isLong = function isLong(obj) {
    return (obj && obj["__isLong__"]) === true;
};

/**
 * A cache of the Long representations of small integer values.
 * @type {!Object}
 * @inner
 */
var INT_CACHE = {};

/**
 * A cache of the Long representations of small unsigned integer values.
 * @type {!Object}
 * @inner
 */
var UINT_CACHE = {};

/**
 * Returns a Long representing the given 32 bit integer value.
 * @param {number} value The 32 bit integer in question
 * @param {boolean=} unsigned Whether unsigned or not, defaults to `false` for signed
 * @returns {!Long} The corresponding Long value
 * @expose
 */
Long.fromInt = function fromInt(value, unsigned) {
    var obj, cachedObj;
    if (!unsigned) {
        value = value | 0;
        if (-128 <= value && value < 128) {
            cachedObj = INT_CACHE[value];
            if (cachedObj)
                return cachedObj;
        }
        obj = new Long(value, value < 0 ? -1 : 0, false);
        if (-128 <= value && value < 128)
            INT_CACHE[value] = obj;
        return obj;
    } else {
        value = value >>> 0;
        if (0 <= value && value < 256) {
            cachedObj = UINT_CACHE[value];
            if (cachedObj)
                return cachedObj;
        }
        obj = new Long(value, (value | 0) < 0 ? -1 : 0, true);
        if (0 <= value && value < 256)
            UINT_CACHE[value] = obj;
        return obj;
    }
};

/**
 * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.
 * @param {number} value The number in question
 * @param {boolean=} unsigned Whether unsigned or not, defaults to `false` for signed
 * @returns {!Long} The corresponding Long value
 * @expose
 */
Long.fromNumber = function fromNumber(value, unsigned) {
    unsigned = !!unsigned;
    if (isNaN(value) || !isFinite(value))
        return Long.ZERO;
    if (!unsigned && value <= -TWO_PWR_63_DBL)
        return Long.MIN_VALUE;
    if (!unsigned && value + 1 >= TWO_PWR_63_DBL)
        return Long.MAX_VALUE;
    if (unsigned && value >= TWO_PWR_64_DBL)
        return Long.MAX_UNSIGNED_VALUE;
    if (value < 0)
        return Long.fromNumber(-value, unsigned).negate();
    return new Long((value % TWO_PWR_32_DBL) | 0, (value / TWO_PWR_32_DBL) | 0, unsigned);
};

/**
 * Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits. Each is
 *  assumed to use 32 bits.
 * @param {number} lowBits The low 32 bits
 * @param {number} highBits The high 32 bits
 * @param {boolean=} unsigned Whether unsigned or not, defaults to `false` for signed
 * @returns {!Long} The corresponding Long value
 * @expose
 */
Long.fromBits = function fromBits(lowBits, highBits, unsigned) {
    return new Long(lowBits, highBits, unsigned);
};

/**
 * Returns a Long representation of the given string, written using the specified radix.
 * @param {string} str The textual representation of the Long
 * @param {(boolean|number)=} unsigned Whether unsigned or not, defaults to `false` for signed
 * @param {number=} radix The radix in which the text is written (2-36), defaults to 10
 * @returns {!Long} The corresponding Long value
 * @expose
 */
Long.fromString = function fromString(str, unsigned, radix) {
    if (str.length === 0)
        throw Error('number format error: empty string');
    if (str === "NaN" || str === "Infinity" || str === "+Infinity" || str === "-Infinity")
        return Long.ZERO;
    if (typeof unsigned === 'number') // For goog.math.long compatibility
        radix = unsigned,
        unsigned = false;
    radix = radix || 10;
    if (radix < 2 || 36 < radix)
        throw Error('radix out of range: ' + radix);

    var p;
    if ((p = str.indexOf('-')) > 0)
        throw Error('number format error: interior "-" character: ' + str);
    else if (p === 0)
        return Long.fromString(str.substring(1), unsigned, radix).negate();

    // Do several (8) digits each time through the loop, so as to
    // minimize the calls to the very expensive emulated div.
    var radixToPower = Long.fromNumber(Math.pow(radix, 8));

    var result = Long.ZERO;
    for (var i = 0; i < str.length; i += 8) {
        var size = Math.min(8, str.length - i);
        var value = parseInt(str.substring(i, i + size), radix);
        if (size < 8) {
            var power = Long.fromNumber(Math.pow(radix, size));
            result = result.multiply(power).add(Long.fromNumber(value));
        } else {
            result = result.multiply(radixToPower);
            result = result.add(Long.fromNumber(value));
        }
    }
    result.unsigned = unsigned;
    return result;
};

/**
 * Converts the specified value to a Long.
 * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val Value
 * @returns {!Long}
 * @expose
 */
Long.fromValue = function fromValue(val) {
    if (val /* is compatible */ instanceof Long)
        return val;
    if (typeof val === 'number')
        return Long.fromNumber(val);
    if (typeof val === 'string')
        return Long.fromString(val);
    // Throws for non-objects, converts non-instanceof Long:
    return new Long(val.low, val.high, val.unsigned);
};

// NOTE: the compiler should inline these constant values below and then remove these variables, so there should be
// no runtime penalty for these.

/**
 * @type {number}
 * @const
 * @inner
 */
var TWO_PWR_16_DBL = 1 << 16;

/**
 * @type {number}
 * @const
 * @inner
 */
var TWO_PWR_24_DBL = 1 << 24;

/**
 * @type {number}
 * @const
 * @inner
 */
var TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;

/**
 * @type {number}
 * @const
 * @inner
 */
var TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;

/**
 * @type {number}
 * @const
 * @inner
 */
var TWO_PWR_63_DBL = TWO_PWR_64_DBL / 2;

/**
 * @type {!Long}
 * @const
 * @inner
 */
var TWO_PWR_24 = Long.fromInt(TWO_PWR_24_DBL);

/**
 * Signed zero.
 * @type {!Long}
 * @expose
 */
Long.ZERO = Long.fromInt(0);

/**
 * Unsigned zero.
 * @type {!Long}
 * @expose
 */
Long.UZERO = Long.fromInt(0, true);

/**
 * Signed one.
 * @type {!Long}
 * @expose
 */
Long.ONE = Long.fromInt(1);

/**
 * Unsigned one.
 * @type {!Long}
 * @expose
 */
Long.UONE = Long.fromInt(1, true);

/**
 * Signed negative one.
 * @type {!Long}
 * @expose
 */
Long.NEG_ONE = Long.fromInt(-1);

/**
 * Maximum signed value.
 * @type {!Long}
 * @expose
 */
Long.MAX_VALUE = Long.fromBits(0xFFFFFFFF|0, 0x7FFFFFFF|0, false);

/**
 * Maximum unsigned value.
 * @type {!Long}
 * @expose
 */
Long.MAX_UNSIGNED_VALUE = Long.fromBits(0xFFFFFFFF|0, 0xFFFFFFFF|0, true);

/**
 * Minimum signed value.
 * @type {!Long}
 * @expose
 */
Long.MIN_VALUE = Long.fromBits(0, 0x80000000|0, false);

/**
 * Converts the Long to a 32 bit integer, assuming it is a 32 bit integer.
 * @returns {number}
 * @expose
 */
Long.prototype.toInt = function toInt() {
    return this.unsigned ? this.low >>> 0 : this.low;
};

/**
 * Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa).
 * @returns {number}
 * @expose
 */
Long.prototype.toNumber = function toNumber() {
    if (this.unsigned) {
        return ((this.high >>> 0) * TWO_PWR_32_DBL) + (this.low >>> 0);
    }
    return this.high * TWO_PWR_32_DBL + (this.low >>> 0);
};

/**
 * Converts the Long to a string written in the specified radix.
 * @param {number=} radix Radix (2-36), defaults to 10
 * @returns {string}
 * @override
 * @throws {RangeError} If `radix` is out of range
 * @expose
 */
Long.prototype.toString = function toString(radix) {
    radix = radix || 10;
    if (radix < 2 || 36 < radix)
        throw RangeError('radix out of range: ' + radix);
    if (this.isZero())
        return '0';
    var rem;
    if (this.isNegative()) { // Unsigned Longs are never negative
        if (this.equals(Long.MIN_VALUE)) {
            // We need to change the Long value before it can be negated, so we remove
            // the bottom-most digit in this base and then recurse to do the rest.
            var radixLong = Long.fromNumber(radix);
            var div = this.divide(radixLong);
            rem = div.multiply(radixLong).subtract(this);
            return div.toString(radix) + rem.toInt().toString(radix);
        } else
            return '-' + this.negate().toString(radix);
    }

    // Do several (6) digits each time through the loop, so as to
    // minimize the calls to the very expensive emulated div.
    var radixToPower = Long.fromNumber(Math.pow(radix, 6), this.unsigned);
    rem = this;
    var result = '';
    while (true) {
        var remDiv = rem.divide(radixToPower),
            intval = rem.subtract(remDiv.multiply(radixToPower)).toInt() >>> 0,
            digits = intval.toString(radix);
        rem = remDiv;
        if (rem.isZero())
            return digits + result;
        else {
            while (digits.length < 6)
                digits = '0' + digits;
            result = '' + digits + result;
        }
    }
};

/**
 * Gets the high 32 bits as a signed integer.
 * @returns {number} Signed high bits
 * @expose
 */
Long.prototype.getHighBits = function getHighBits() {
    return this.high;
};

/**
 * Gets the high 32 bits as an unsigned integer.
 * @returns {number} Unsigned high bits
 * @expose
 */
Long.prototype.getHighBitsUnsigned = function getHighBitsUnsigned() {
    return this.high >>> 0;
};

/**
 * Gets the low 32 bits as a signed integer.
 * @returns {number} Signed low bits
 * @expose
 */
Long.prototype.getLowBits = function getLowBits() {
    return this.low;
};

/**
 * Gets the low 32 bits as an unsigned integer.
 * @returns {number} Unsigned low bits
 * @expose
 */
Long.prototype.getLowBitsUnsigned = function getLowBitsUnsigned() {
    return this.low >>> 0;
};

/**
 * Gets the number of bits needed to represent the absolute value of this Long.
 * @returns {number}
 * @expose
 */
Long.prototype.getNumBitsAbs = function getNumBitsAbs() {
    if (this.isNegative()) // Unsigned Longs are never negative
        return this.equals(Long.MIN_VALUE) ? 64 : this.negate().getNumBitsAbs();
    var val = this.high != 0 ? this.high : this.low;
    for (var bit = 31; bit > 0; bit--)
        if ((val & (1 << bit)) != 0)
            break;
    return this.high != 0 ? bit + 33 : bit + 1;
};

/**
 * Tests if this Long's value equals zero.
 * @returns {boolean}
 * @expose
 */
Long.prototype.isZero = function isZero() {
    return this.high === 0 && this.low === 0;
};

/**
 * Tests if this Long's value is negative.
 * @returns {boolean}
 * @expose
 */
Long.prototype.isNegative = function isNegative() {
    return !this.unsigned && this.high < 0;
};

/**
 * Tests if this Long's value is positive.
 * @returns {boolean}
 * @expose
 */
Long.prototype.isPositive = function isPositive() {
    return this.unsigned || this.high >= 0;
};

/**
 * Tests if this Long's value is odd.
 * @returns {boolean}
 * @expose
 */
Long.prototype.isOdd = function isOdd() {
    return (this.low & 1) === 1;
};

/**
 * Tests if this Long's value is even.
 * @returns {boolean}
 * @expose
 */
Long.prototype.isEven = function isEven() {
    return (this.low & 1) === 0;
};

/**
 * Tests if this Long's value equals the specified's.
 * @param {!Long|number|string} other Other value
 * @returns {boolean}
 * @expose
 */
Long.prototype.equals = function equals(other) {
    if (!Long.isLong(other))
        other = Long.fromValue(other);
    if (this.unsigned !== other.unsigned && (this.high >>> 31) === 1 && (other.high >>> 31) === 1)
        return false;
    return this.high === other.high && this.low === other.low;
};

/**
 * Tests if this Long's value equals the specified's. This is an alias of {@link Long#equals}.
 * @function
 * @param {!Long|number|string} other Other value
 * @returns {boolean}
 * @expose
 */
Long.eq = Long.prototype.equals;

/**
 * Tests if this Long's value differs from the specified's.
 * @param {!Long|number|string} other Other value
 * @returns {boolean}
 * @expose
 */
Long.prototype.notEquals = function notEquals(other) {
    return !this.equals(/* validates */ other);
};

/**
 * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.
 * @function
 * @param {!Long|number|string} other Other value
 * @returns {boolean}
 * @expose
 */
Long.neq = Long.prototype.notEquals;

/**
 * Tests if this Long's value is less than the specified's.
 * @param {!Long|number|string} other Other value
 * @returns {boolean}
 * @expose
 */
Long.prototype.lessThan = function lessThan(other) {
    return this.compare(/* validates */ other) < 0;
};

/**
 * Tests if this Long's value is less than the specified's. This is an alias of {@link Long#lessThan}.
 * @function
 * @param {!Long|number|string} other Other value
 * @returns {boolean}
 * @expose
 */
Long.prototype.lt = Long.prototype.lessThan;

/**
 * Tests if this Long's value is less than or equal the specified's.
 * @param {!Long|number|string} other Other value
 * @returns {boolean}
 * @expose
 */
Long.prototype.lessThanOrEqual = function lessThanOrEqual(other) {
    return this.compare(/* validates */ other) <= 0;
};

/**
 * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.
 * @function
 * @param {!Long|number|string} other Other value
 * @returns {boolean}
 * @expose
 */
Long.prototype.lte = Long.prototype.lessThanOrEqual;

/**
 * Tests if this Long's value is greater than the specified's.
 * @param {!Long|number|string} other Other value
 * @returns {boolean}
 * @expose
 */
Long.prototype.greaterThan = function greaterThan(other) {
    return this.compare(/* validates */ other) > 0;
};

/**
 * Tests if this Long's value is greater than the specified's. This is an alias of {@link Long#greaterThan}.
 * @function
 * @param {!Long|number|string} other Other value
 * @returns {boolean}
 * @expose
 */
Long.prototype.gt = Long.prototype.greaterThan;

/**
 * Tests if this Long's value is greater than or equal the specified's.
 * @param {!Long|number|string} other Other value
 * @returns {boolean}
 * @expose
 */
Long.prototype.greaterThanOrEqual = function greaterThanOrEqual(other) {
    return this.compare(/* validates */ other) >= 0;
};

/**
 * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.
 * @function
 * @param {!Long|number|string} other Other value
 * @returns {boolean}
 * @expose
 */
Long.prototype.gte = Long.prototype.greaterThanOrEqual;

/**
 * Compares this Long's value with the specified's.
 * @param {!Long|number|string} other Other value
 * @returns {number} 0 if they are the same, 1 if the this is greater and -1
 *  if the given one is greater
 * @expose
 */
Long.prototype.compare = function compare(other) {
    if (!Long.isLong(other))
        other = Long.fromValue(other);
    if (this.equals(other))
        return 0;
    var thisNeg = this.isNegative(),
        otherNeg = other.isNegative();
    if (thisNeg && !otherNeg)
        return -1;
    if (!thisNeg && otherNeg)
        return 1;
    // At this point the sign bits are the same
    if (!this.unsigned)
        return this.subtract(other).isNegative() ? -1 : 1;
    // Both are positive if at least one is unsigned
    return (other.high >>> 0) > (this.high >>> 0) || (other.high === this.high && (other.low >>> 0) > (this.low >>> 0)) ? -1 : 1;
};

/**
 * Negates this Long's value.
 * @returns {!Long} Negated Long
 * @expose
 */
Long.prototype.negate = function negate() {
    if (!this.unsigned && this.equals(Long.MIN_VALUE))
        return Long.MIN_VALUE;
    return this.not().add(Long.ONE);
};

/**
 * Negates this Long's value. This is an alias of {@link Long#negate}.
 * @function
 * @returns {!Long} Negated Long
 * @expose
 */
Long.prototype.neg = Long.prototype.negate;

/**
 * Returns the sum of this and the specified Long.
 * @param {!Long|number|string} addend Addend
 * @returns {!Long} Sum
 * @expose
 */
Long.prototype.add = function add(addend) {
    if (!Long.isLong(addend))
        addend = Long.fromValue(addend);

    // Divide each number into 4 chunks of 16 bits, and then sum the chunks.

    var a48 = this.high >>> 16;
    var a32 = this.high & 0xFFFF;
    var a16 = this.low >>> 16;
    var a00 = this.low & 0xFFFF;

    var b48 = addend.high >>> 16;
    var b32 = addend.high & 0xFFFF;
    var b16 = addend.low >>> 16;
    var b00 = addend.low & 0xFFFF;

    var c48 = 0, c32 = 0, c16 = 0, c00 = 0;
    c00 += a00 + b00;
    c16 += c00 >>> 16;
    c00 &= 0xFFFF;
    c16 += a16 + b16;
    c32 += c16 >>> 16;
    c16 &= 0xFFFF;
    c32 += a32 + b32;
    c48 += c32 >>> 16;
    c32 &= 0xFFFF;
    c48 += a48 + b48;
    c48 &= 0xFFFF;
    return Long.fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);
};

/**
 * Returns the difference of this and the specified Long.
 * @param {!Long|number|string} subtrahend Subtrahend
 * @returns {!Long} Difference
 * @expose
 */
Long.prototype.subtract = function subtract(subtrahend) {
    if (!Long.isLong(subtrahend))
        subtrahend = Long.fromValue(subtrahend);
    return this.add(subtrahend.negate());
};

/**
 * Returns the difference of this and the specified Long. This is an alias of {@link Long#subtract}.
 * @function
 * @param {!Long|number|string} subtrahend Subtrahend
 * @returns {!Long} Difference
 * @expose
 */
Long.prototype.sub = Long.prototype.subtract;

/**
 * Returns the product of this and the specified Long.
 * @param {!Long|number|string} multiplier Multiplier
 * @returns {!Long} Product
 * @expose
 */
Long.prototype.multiply = function multiply(multiplier) {
    if (this.isZero())
        return Long.ZERO;
    if (!Long.isLong(multiplier))
        multiplier = Long.fromValue(multiplier);
    if (multiplier.isZero())
        return Long.ZERO;
    if (this.equals(Long.MIN_VALUE))
        return multiplier.isOdd() ? Long.MIN_VALUE : Long.ZERO;
    if (multiplier.equals(Long.MIN_VALUE))
        return this.isOdd() ? Long.MIN_VALUE : Long.ZERO;

    if (this.isNegative()) {
        if (multiplier.isNegative())
            return this.negate().multiply(multiplier.negate());
        else
            return this.negate().multiply(multiplier).negate();
    } else if (multiplier.isNegative())
        return this.multiply(multiplier.negate()).negate();

    // If both longs are small, use float multiplication
    if (this.lessThan(TWO_PWR_24) && multiplier.lessThan(TWO_PWR_24))
        return Long.fromNumber(this.toNumber() * multiplier.toNumber(), this.unsigned);

    // Divide each long into 4 chunks of 16 bits, and then add up 4x4 products.
    // We can skip products that would overflow.

    var a48 = this.high >>> 16;
    var a32 = this.high & 0xFFFF;
    var a16 = this.low >>> 16;
    var a00 = this.low & 0xFFFF;

    var b48 = multiplier.high >>> 16;
    var b32 = multiplier.high & 0xFFFF;
    var b16 = multiplier.low >>> 16;
    var b00 = multiplier.low & 0xFFFF;

    var c48 = 0, c32 = 0, c16 = 0, c00 = 0;
    c00 += a00 * b00;
    c16 += c00 >>> 16;
    c00 &= 0xFFFF;
    c16 += a16 * b00;
    c32 += c16 >>> 16;
    c16 &= 0xFFFF;
    c16 += a00 * b16;
    c32 += c16 >>> 16;
    c16 &= 0xFFFF;
    c32 += a32 * b00;
    c48 += c32 >>> 16;
    c32 &= 0xFFFF;
    c32 += a16 * b16;
    c48 += c32 >>> 16;
    c32 &= 0xFFFF;
    c32 += a00 * b32;
    c48 += c32 >>> 16;
    c32 &= 0xFFFF;
    c48 += a48 * b00 + a32 * b16 + a16 * b32 + a00 * b48;
    c48 &= 0xFFFF;
    return Long.fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);
};

/**
 * Returns the product of this and the specified Long. This is an alias of {@link Long#multiply}.
 * @function
 * @param {!Long|number|string} multiplier Multiplier
 * @returns {!Long} Product
 * @expose
 */
Long.prototype.mul = Long.prototype.multiply;

/**
 * Returns this Long divided by the specified.
 * @param {!Long|number|string} divisor Divisor
 * @returns {!Long} Quotient
 * @expose
 */
Long.prototype.divide = function divide(divisor) {
    if (!Long.isLong(divisor))
        divisor = Long.fromValue(divisor);
    if (divisor.isZero())
        throw(new Error('division by zero'));
    if (this.isZero())
        return this.unsigned ? Long.UZERO : Long.ZERO;
    var approx, rem, res;
    if (this.equals(Long.MIN_VALUE)) {
        if (divisor.equals(Long.ONE) || divisor.equals(Long.NEG_ONE))
            return Long.MIN_VALUE;  // recall that -MIN_VALUE == MIN_VALUE
        else if (divisor.equals(Long.MIN_VALUE))
            return Long.ONE;
        else {
            // At this point, we have |other| >= 2, so |this/other| < |MIN_VALUE|.
            var halfThis = this.shiftRight(1);
            approx = halfThis.divide(divisor).shiftLeft(1);
            if (approx.equals(Long.ZERO)) {
                return divisor.isNegative() ? Long.ONE : Long.NEG_ONE;
            } else {
                rem = this.subtract(divisor.multiply(approx));
                res = approx.add(rem.divide(divisor));
                return res;
            }
        }
    } else if (divisor.equals(Long.MIN_VALUE))
        return this.unsigned ? Long.UZERO : Long.ZERO;
    if (this.isNegative()) {
        if (divisor.isNegative())
            return this.negate().divide(divisor.negate());
        return this.negate().divide(divisor).negate();
    } else if (divisor.isNegative())
        return this.divide(divisor.negate()).negate();

    // Repeat the following until the remainder is less than other:  find a
    // floating-point that approximates remainder / other *from below*, add this
    // into the result, and subtract it from the remainder.  It is critical that
    // the approximate value is less than or equal to the real value so that the
    // remainder never becomes negative.
    res = Long.ZERO;
    rem = this;
    while (rem.greaterThanOrEqual(divisor)) {
        // Approximate the result of division. This may be a little greater or
        // smaller than the actual value.
        approx = Math.max(1, Math.floor(rem.toNumber() / divisor.toNumber()));

        // We will tweak the approximate result by changing it in the 48-th digit or
        // the smallest non-fractional digit, whichever is larger.
        var log2 = Math.ceil(Math.log(approx) / Math.LN2),
            delta = (log2 <= 48) ? 1 : Math.pow(2, log2 - 48),

        // Decrease the approximation until it is smaller than the remainder.  Note
        // that if it is too large, the product overflows and is negative.
            approxRes = Long.fromNumber(approx),
            approxRem = approxRes.multiply(divisor);
        while (approxRem.isNegative() || approxRem.greaterThan(rem)) {
            approx -= delta;
            approxRes = Long.fromNumber(approx, this.unsigned);
            approxRem = approxRes.multiply(divisor);
        }

        // We know the answer can't be zero... and actually, zero would cause
        // infinite recursion since we would make no progress.
        if (approxRes.isZero())
            approxRes = Long.ONE;

        res = res.add(approxRes);
        rem = rem.subtract(approxRem);
    }
    return res;
};

/**
 * Returns this Long divided by the specified. This is an alias of {@link Long#divide}.
 * @function
 * @param {!Long|number|string} divisor Divisor
 * @returns {!Long} Quotient
 * @expose
 */
Long.prototype.div = Long.prototype.divide;

/**
 * Returns this Long modulo the specified.
 * @param {!Long|number|string} divisor Divisor
 * @returns {!Long} Remainder
 * @expose
 */
Long.prototype.modulo = function modulo(divisor) {
    if (!Long.isLong(divisor))
        divisor = Long.fromValue(divisor);
    return this.subtract(this.divide(divisor).multiply(divisor));
};

/**
 * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.
 * @function
 * @param {!Long|number|string} divisor Divisor
 * @returns {!Long} Remainder
 * @expose
 */
Long.prototype.mod = Long.prototype.modulo;

/**
 * Returns the bitwise NOT of this Long.
 * @returns {!Long}
 * @expose
 */
Long.prototype.not = function not() {
    return Long.fromBits(~this.low, ~this.high, this.unsigned);
};

/**
 * Returns the bitwise AND of this Long and the specified.
 * @param {!Long|number|string} other Other Long
 * @returns {!Long}
 * @expose
 */
Long.prototype.and = function and(other) {
    if (!Long.isLong(other))
        other = Long.fromValue(other);
    return Long.fromBits(this.low & other.low, this.high & other.high, this.unsigned);
};

/**
 * Returns the bitwise OR of this Long and the specified.
 * @param {!Long|number|string} other Other Long
 * @returns {!Long}
 * @expose
 */
Long.prototype.or = function or(other) {
    if (!Long.isLong(other))
        other = Long.fromValue(other);
    return Long.fromBits(this.low | other.low, this.high | other.high, this.unsigned);
};

/**
 * Returns the bitwise XOR of this Long and the given one.
 * @param {!Long|number|string} other Other Long
 * @returns {!Long}
 * @expose
 */
Long.prototype.xor = function xor(other) {
    if (!Long.isLong(other))
        other = Long.fromValue(other);
    return Long.fromBits(this.low ^ other.low, this.high ^ other.high, this.unsigned);
};

/**
 * Returns this Long with bits shifted to the left by the given amount.
 * @param {number|!Long} numBits Number of bits
 * @returns {!Long} Shifted Long
 * @expose
 */
Long.prototype.shiftLeft = function shiftLeft(numBits) {
    if (Long.isLong(numBits))
        numBits = numBits.toInt();
    if ((numBits &= 63) === 0)
        return this;
    else if (numBits < 32)
        return Long.fromBits(this.low << numBits, (this.high << numBits) | (this.low >>> (32 - numBits)), this.unsigned);
    else
        return Long.fromBits(0, this.low << (numBits - 32), this.unsigned);
};

/**
 * Returns this Long with bits shifted to the left by the given amount. This is an alias of {@link Long#shiftLeft}.
 * @function
 * @param {number|!Long} numBits Number of bits
 * @returns {!Long} Shifted Long
 * @expose
 */
Long.prototype.shl = Long.prototype.shiftLeft;

/**
 * Returns this Long with bits arithmetically shifted to the right by the given amount.
 * @param {number|!Long} numBits Number of bits
 * @returns {!Long} Shifted Long
 * @expose
 */
Long.prototype.shiftRight = function shiftRight(numBits) {
    if (Long.isLong(numBits))
        numBits = numBits.toInt();
    if ((numBits &= 63) === 0)
        return this;
    else if (numBits < 32)
        return Long.fromBits((this.low >>> numBits) | (this.high << (32 - numBits)), this.high >> numBits, this.unsigned);
    else
        return Long.fromBits(this.high >> (numBits - 32), this.high >= 0 ? 0 : -1, this.unsigned);
};

/**
 * Returns this Long with bits arithmetically shifted to the right by the given amount. This is an alias of {@link Long#shiftRight}.
 * @function
 * @param {number|!Long} numBits Number of bits
 * @returns {!Long} Shifted Long
 * @expose
 */
Long.prototype.shr = Long.prototype.shiftRight;

/**
 * Returns this Long with bits logically shifted to the right by the given amount.
 * @param {number|!Long} numBits Number of bits
 * @returns {!Long} Shifted Long
 * @expose
 */
Long.prototype.shiftRightUnsigned = function shiftRightUnsigned(numBits) {
    if (Long.isLong(numBits))
        numBits = numBits.toInt();
    numBits &= 63;
    if (numBits === 0)
        return this;
    else {
        var high = this.high;
        if (numBits < 32) {
            var low = this.low;
            return Long.fromBits((low >>> numBits) | (high << (32 - numBits)), high >>> numBits, this.unsigned);
        } else if (numBits === 32)
            return Long.fromBits(high, 0, this.unsigned);
        else
            return Long.fromBits(high >>> (numBits - 32), 0, this.unsigned);
    }
};

/**
 * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.
 * @function
 * @param {number|!Long} numBits Number of bits
 * @returns {!Long} Shifted Long
 * @expose
 */
Long.prototype.shru = Long.prototype.shiftRightUnsigned;

/**
 * Converts this Long to signed.
 * @returns {!Long} Signed long
 * @expose
 */
Long.prototype.toSigned = function toSigned() {
    if (!this.unsigned)
        return this;
    return new Long(this.low, this.high, false);
};

/**
 * Converts this Long to unsigned.
 * @returns {!Long} Unsigned long
 * @expose
 */
Long.prototype.toUnsigned = function toUnsigned() {
    if (this.unsigned)
        return this;
    return new Long(this.low, this.high, true);
};
