<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
		 bootstrap="vendor/autoload.php"
		 cacheResultFile=".build/phpunit.result.cache"
		 colors="true"
		 verbose="true"
>
	<filter>
		<whitelist processUncoveredFilesFromWhitelist="true">
			<directory suffix=".php">./src</directory>
		</whitelist>
	</filter>
	<testsuites>
		<testsuite name="php-qrcode test suite">
			<directory suffix=".php">./tests/</directory>
		</testsuite>
	</testsuites>
	<logging>
		<log type="coverage-clover" target=".build/coverage/clover.xml"/>
		<log type="coverage-xml" target=".build/coverage/coverage-xml"/>
		<log type="junit" target=".build/logs/junit.xml"/>
	</logging>
</phpunit>
