# lodash.flatten v4.4.0

The [lodash](https://lodash.com/) method `_.flatten` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.flatten
```

In Node.js:
```js
var flatten = require('lodash.flatten');
```

See the [documentation](https://lodash.com/docs#flatten) or [package source](https://github.com/lodash/lodash/blob/4.4.0-npm-packages/lodash.flatten) for more details.
