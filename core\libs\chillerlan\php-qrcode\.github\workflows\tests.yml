# https://help.github.com/en/categories/automating-your-workflow-with-github-actions
# https://github.com/se<PERSON><PERSON><PERSON><PERSON>/phpunit/blob/master/.github/workflows/ci.yml

on:
  - pull_request
  - push

name: "Continuous Integration"

jobs:

  static-code-analysis:
    name: "Static Code Analysis"

    runs-on: ubuntu-latest

    env:
      PHAN_ALLOW_XDEBUG: 0
      PHAN_DISABLE_XDEBUG_WARN: 1

    steps:
      - name: "Checkout"
        uses: actions/checkout@v2

      - name: "Install PHP"
        uses: shivammathur/setup-php@v2
        with:
          php-version: "7.4"
          coverage: none
          tools: pecl
          extensions: ast, gd, imagick, json, mbstring

      - name: "Update dependencies with composer"
        run: composer update --no-interaction --no-ansi --no-progress --no-suggest

      - name: "Run phan"
        run: php vendor/bin/phan

  tests:
    name: "Unit Tests"

    runs-on: ${{ matrix.os }}

    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-latest
#          - windows-latest
        php-version:
          - "7.2"
          - "7.3"
          - "7.4"
          - "8.0"

    steps:
      - name: "Checkout"
        uses: actions/checkout@v2

      - name: "Install PHP with extensions"
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          coverage: pcov
          tools: pecl
          extensions: gd, imagick, json, mbstring

      - name: "Install dependencies with composer"
        run: composer update --no-ansi --no-interaction --no-progress --no-suggest

      - name: "Run tests with phpunit"
        run: php vendor/phpunit/phpunit/phpunit --configuration=phpunit.xml

      - name: "Send code coverage report to Codecov.io"
        uses: codecov/codecov-action@v1
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
