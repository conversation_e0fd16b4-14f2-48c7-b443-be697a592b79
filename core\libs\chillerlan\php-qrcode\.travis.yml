branches:
  only:
    - main
    - v3.2.x

addons:
  apt:
    packages:
      - imagemagick

language: php

matrix:
  include:
    - php: 7.2
    - php: 7.3
    - php: 7.4

before_install:
  - pecl channel-update pecl.php.net
  - printf "\n" | pecl install imagick
install: travis_retry composer install --no-interaction --prefer-source
script: vendor/bin/phpunit --configuration phpunit.xml --coverage-clover clover.xml
after_script: bash <(curl -s https://codecov.io/bash)
