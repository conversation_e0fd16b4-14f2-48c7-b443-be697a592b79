# lodash.reject v4.6.0

The [lodash](https://lodash.com/) method `_.reject` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.reject
```

In Node.js:
```js
var reject = require('lodash.reject');
```

See the [documentation](https://lodash.com/docs#reject) or [package source](https://github.com/lodash/lodash/blob/4.6.0-npm-packages/lodash.reject) for more details.
