<?xml version="1.0"?>
<ruleset name="codemasher/php-settings-container PMD ruleset"
		 xmlns="http://pmd.sf.net/ruleset/1.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://pmd.sf.net/ruleset/1.0.0 http://pmd.sf.net/ruleset_xml_schema.xsd"
		 xsi:noNamespaceSchemaLocation="http://pmd.sf.net/ruleset_xml_schema.xsd">
	<description>codemasher/php-settings-container PMD ruleset</description>
	<exclude-pattern>*/examples/*</exclude-pattern>
	<exclude-pattern>*/tests/*</exclude-pattern>
	<exclude-pattern>*/vendor/*</exclude-pattern>
	<rule ref="rulesets/cleancode.xml">
		<exclude name="BooleanArgumentFlag"/>
	</rule>
	<rule ref="rulesets/codesize.xml/CyclomaticComplexity">
		<priority>1</priority>
		<properties>
			<property name="maximum" value="150" />
		</properties>
	</rule>
	<rule ref="rulesets/controversial.xml">
		<exclude name="CamelCaseMethodName"/>
		<exclude name="CamelCasePropertyName"/>
		<exclude name="CamelCaseParameterName"/>
		<exclude name="CamelCaseVariableName"/>
	</rule>
	<rule ref="rulesets/design.xml">
	</rule>
	<rule ref="rulesets/naming.xml">
		<exclude name="LongVariable"/>
		<exclude name="ShortVariable"/>
	</rule>
	<rule ref="rulesets/unusedcode.xml">
		<exclude name="UnusedFormalParameter"/>
	</rule>
</ruleset>
