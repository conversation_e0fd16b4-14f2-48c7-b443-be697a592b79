"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.stirlingS2Dependencies = void 0;

var _dependenciesBignumberGenerated = require("./dependenciesBignumber.generated.js");

var _dependenciesAddScalarGenerated = require("./dependenciesAddScalar.generated.js");

var _dependenciesCombinationsGenerated = require("./dependenciesCombinations.generated.js");

var _dependenciesDivideScalarGenerated = require("./dependenciesDivideScalar.generated.js");

var _dependenciesFactorialGenerated = require("./dependenciesFactorial.generated.js");

var _dependenciesIsIntegerGenerated = require("./dependenciesIsInteger.generated.js");

var _dependenciesIsNegativeGenerated = require("./dependenciesIsNegative.generated.js");

var _dependenciesLargerGenerated = require("./dependenciesLarger.generated.js");

var _dependenciesMultiplyScalarGenerated = require("./dependenciesMultiplyScalar.generated.js");

var _dependenciesNumberGenerated = require("./dependenciesNumber.generated.js");

var _dependenciesPowGenerated = require("./dependenciesPow.generated.js");

var _dependenciesSubtractGenerated = require("./dependenciesSubtract.generated.js");

var _dependenciesTypedGenerated = require("./dependenciesTyped.generated.js");

var _factoriesAny = require("../../factoriesAny.js");

/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
var stirlingS2Dependencies = {
  bignumberDependencies: _dependenciesBignumberGenerated.bignumberDependencies,
  addScalarDependencies: _dependenciesAddScalarGenerated.addScalarDependencies,
  combinationsDependencies: _dependenciesCombinationsGenerated.combinationsDependencies,
  divideScalarDependencies: _dependenciesDivideScalarGenerated.divideScalarDependencies,
  factorialDependencies: _dependenciesFactorialGenerated.factorialDependencies,
  isIntegerDependencies: _dependenciesIsIntegerGenerated.isIntegerDependencies,
  isNegativeDependencies: _dependenciesIsNegativeGenerated.isNegativeDependencies,
  largerDependencies: _dependenciesLargerGenerated.largerDependencies,
  multiplyScalarDependencies: _dependenciesMultiplyScalarGenerated.multiplyScalarDependencies,
  numberDependencies: _dependenciesNumberGenerated.numberDependencies,
  powDependencies: _dependenciesPowGenerated.powDependencies,
  subtractDependencies: _dependenciesSubtractGenerated.subtractDependencies,
  typedDependencies: _dependenciesTypedGenerated.typedDependencies,
  createStirlingS2: _factoriesAny.createStirlingS2
};
exports.stirlingS2Dependencies = stirlingS2Dependencies;