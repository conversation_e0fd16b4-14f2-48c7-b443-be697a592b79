<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit031ad784801a760d7c5dbfa6feb3e9c6
{
    public static $prefixLengthsPsr4 = array (
        'c' => 
        array (
            'chillerlan\\Settings\\' => 20,
            'chillerlan\\QRCode\\' => 18,
        ),
        'S' => 
        array (
            'Stripe\\' => 7,
        ),
        'P' => 
        array (
            'Piggly\\Pix\\' => 11,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'chillerlan\\Settings\\' => 
        array (
            0 => __DIR__ . '/..' . '/chillerlan/php-settings-container/src',
        ),
        'chillerlan\\QRCode\\' => 
        array (
            0 => __DIR__ . '/..' . '/chillerlan/php-qrcode/src',
        ),
        'Stripe\\' => 
        array (
            0 => __DIR__ . '/..' . '/stripe/stripe-php/lib',
        ),
        'Piggly\\Pix\\' => 
        array (
            0 => __DIR__ . '/..' . '/piggly/php-pix/src',
        ),
    );

    public static $prefixesPsr0 = array (
        'D' => 
        array (
            'Detection' => 
            array (
                0 => __DIR__ . '/..' . '/mobiledetect/mobiledetectlib/namespaced',
            ),
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Mobile_Detect' => __DIR__ . '/..' . '/mobiledetect/mobiledetectlib/Mobile_Detect.php',
        'Paymentwall_ApiObject' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/ApiObject.php',
        'Paymentwall_ApiObjectInterface' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/ApiObjectInterface.php',
        'Paymentwall_Base' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Base.php',
        'Paymentwall_Card' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Card.php',
        'Paymentwall_Charge' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Charge.php',
        'Paymentwall_Config' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Config.php',
        'Paymentwall_GenerericApiObject' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/GenerericApiObject.php',
        'Paymentwall_HttpAction' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/HttpAction.php',
        'Paymentwall_Instance' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Instance.php',
        'Paymentwall_Mobiamo' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Mobiamo.php',
        'Paymentwall_OneTimeToken' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/OneTimeToken.php',
        'Paymentwall_Pingback' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Pingback.php',
        'Paymentwall_Product' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Product.php',
        'Paymentwall_Response_Abstract' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Response/Abstract.php',
        'Paymentwall_Response_Error' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Response/Error.php',
        'Paymentwall_Response_Factory' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Response/Factory.php',
        'Paymentwall_Response_Interface' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Response/Interface.php',
        'Paymentwall_Response_Success' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Response/Success.php',
        'Paymentwall_Signature_Abstract' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Signature/Abstract.php',
        'Paymentwall_Signature_Pingback' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Signature/Pingback.php',
        'Paymentwall_Signature_Widget' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Signature/Widget.php',
        'Paymentwall_Subscription' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Subscription.php',
        'Paymentwall_Widget' => __DIR__ . '/..' . '/paymentwall/paymentwall-php/lib/Paymentwall/Widget.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit031ad784801a760d7c5dbfa6feb3e9c6::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit031ad784801a760d7c5dbfa6feb3e9c6::$prefixDirsPsr4;
            $loader->prefixesPsr0 = ComposerStaticInit031ad784801a760d7c5dbfa6feb3e9c6::$prefixesPsr0;
            $loader->classMap = ComposerStaticInit031ad784801a760d7c5dbfa6feb3e9c6::$classMap;

        }, null, ClassLoader::class);
    }
}
